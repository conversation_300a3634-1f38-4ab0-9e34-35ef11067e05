import { useRef, useState, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { isTrackReference, VideoTrack } from '@livekit/components-react';



export function usePictureInPicture({
  room,
  tracks,
  isTrackReference: isTrackReferenceCheck,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Simple configuration
  const defaultConfig = useMemo(() => ({
    width: 320,
    height: 240
  }), []);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Get current track to display - screen share first, then camera
  const currentTrack = useMemo(() => {
    if (!tracks?.length) {
      console.log('PiP: No tracks available');
      return null;
    }

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReferenceCheck)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      console.log('PiP: Showing screen share');
      return screenShareTracks[0];
    }

    // Priority 2: Local camera
    const localCameraTracks = tracks
      .filter(isTrackReferenceCheck)
      .filter((track) =>
        track.publication.source === Track.Source.Camera &&
        track.participant.isLocal
      );

    if (localCameraTracks.length > 0) {
      const cameraTrack = localCameraTracks[0];
      const isEnabled = !cameraTrack.publication.isMuted && cameraTrack.publication.isSubscribed;
      console.log(`PiP: Camera track found - enabled: ${isEnabled}, muted: ${cameraTrack.publication.isMuted}`);

      // Only return camera track if it's enabled (not muted and subscribed)
      if (isEnabled) {
        return cameraTrack;
      } else {
        console.log('PiP: Camera is muted/disabled - not showing');
        return null;
      }
    }

    console.log('PiP: No valid tracks found');
    return null;
  }, [tracks, isTrackReferenceCheck, room]);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);



  // Custom PiP Content with aspect ratio logic
  const PipContent = useCallback(() => {
    if (!currentTrack) {
      console.log('PiP: No current track - showing nothing');
      return null; // No fallback - don't show anything when no tracks available
    }

    console.log('PiP: Rendering ParticipantTile with aspect ratio logic');

    // Check if we have a valid LiveKit track reference
    const isValidTrackRef = isTrackReference(currentTrack) &&
      currentTrack.publication &&
      currentTrack.participant;

    if (!isValidTrackRef) {
      console.log('PiP: Invalid track reference');
      return null;
    }

    // Aspect ratio calculation logic
    const calculateAspectRatio = () => {
      const pipWindow = pipWindowRef.current;
      if (!pipWindow) return {
        borderSize: 8,
        containerWidth: 304,
        containerHeight: 224,
        videoWidth: 304,
        videoHeight: 171
      };

      const windowWidth = pipWindow.innerWidth;
      const windowHeight = pipWindow.innerHeight;

      // Minimal border - reduce to ensure video is visible even in small PiP
      const borderSize = Math.max(4, Math.min(8, Math.min(windowWidth, windowHeight) * 0.02));
      const containerWidth = windowWidth - (borderSize * 2);
      const containerHeight = windowHeight - (borderSize * 2);

      // Don't force 16:9 - let video use available space with proper object-fit
      // This ensures video is always visible and properly scaled
      return {
        borderSize,
        containerWidth,
        containerHeight,
        videoWidth: containerWidth,
        videoHeight: containerHeight
      };
    };

    const dimensions = calculateAspectRatio();

    return (
      <div style={{
        width: '100%',
        height: '100%',
        background: '#000',
        padding: `${dimensions.borderSize}px`,
        boxSizing: 'border-box'
      }}>
        {/* Container with border - video fills entire container */}
        <div style={{
          width: `${dimensions.containerWidth}px`,
          height: `${dimensions.containerHeight}px`,
          position: 'relative',
          backgroundColor: '#000',
          borderRadius: '8px',
          border: '2px solid #2196F3',
          overflow: 'hidden'
        }}>
          {/* Video fills container with proper object-fit */}
          <VideoTrack
            trackRef={currentTrack}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '4px'
            }}
          />
        </div>
      </div>
    );
  }, [currentTrack]);

  // Simple styles
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100%;
      height: 100%;
      background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Ensure VideoTrack fills container properly */
    .lk-video-track {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
    }

    .lk-video-track video {
      object-fit: cover !important;
      width: 100% !important;
      height: 100% !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
    }

    /* Ensure any video element uses cover */
    video {
      object-fit: cover !important;
      width: 100% !important;
      height: 100% !important;
    }
  `, []);

  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported
  };
}